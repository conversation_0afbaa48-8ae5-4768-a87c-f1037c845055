@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 40 30% 98%;
    --foreground: 20 10% 10%;
    
    --card: 40 30% 98%;
    --card-foreground: 20 10% 10%;
    
    --popover: 40 30% 98%;
    --popover-foreground: 20 10% 10%;
    
    --primary: 175 80% 30%;
    --primary-foreground: 40 30% 98%;
    
    --secondary: 20 30% 85%;
    --secondary-foreground: 20 10% 10%;
    
    --accent: 25 80% 50%;
    --accent-foreground: 40 30% 98%;
    
    --muted: 20 10% 92%;
    --muted-foreground: 20 10% 40%;
    
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 20 10% 85%;
    --input: 20 10% 85%;
    --ring: 175 80% 30%;
    
    --radius: 0.5rem;
  }

  .dark {
    --background: 20 10% 10%;
    --foreground: 40 30% 98%;
    
    --card: 20 10% 10%;
    --card-foreground: 40 30% 98%;
    
    --popover: 20 10% 10%;
    --popover-foreground: 40 30% 98%;
    
    --primary: 175 80% 30%;
    --primary-foreground: 40 30% 98%;
    
    --secondary: 20 10% 20%;
    --secondary-foreground: 40 30% 98%;
    
    --accent: 25 80% 50%;
    --accent-foreground: 40 30% 98%;
    
    --muted: 20 10% 20%;
    --muted-foreground: 20 10% 60%;
    
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    
    --border: 20 10% 25%;
    --input: 20 10% 25%;
    --ring: 175 80% 30%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: 'Playfair Display', serif;
  }

  body, p, span, div, button, input {
    font-family: 'Poppins', sans-serif;
  }
}

/* Custom Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInLeft {
  from { transform: translateX(-100%); }
  to { transform: translateX(0); }
}

@keyframes slideInRight {
  from { transform: translateX(100%); }
  to { transform: translateX(0); }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slideInLeft {
  animation: slideInLeft 0.3s ease-in-out;
}

.animate-slideInRight {
  animation: slideInRight 0.3s ease-in-out;
}

/* Hover effects */
.hover-lift {
  @apply transition-transform duration-300;
}

.hover-lift:hover {
  transform: translateY(-5px);
}

/* Gold accent elements */
.gold-accent {
  @apply bg-gradient-to-r from-amber-500 to-yellow-600;
}

.text-gold {
  @apply bg-gradient-to-r from-amber-500 to-yellow-600 bg-clip-text text-transparent;
}

/* Custom scroll for sidebar */
.custom-scrollbar::-webkit-scrollbar {
  width: 5px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  @apply bg-transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  @apply bg-muted rounded-full;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  @apply bg-primary/50;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}
