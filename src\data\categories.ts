import { Category } from '../types';

// Icons to be used for categories
// Will use Lucide icons in the actual components
const ICONS = {
  venues: 'Building',
  decoration: 'Sparkles',
  photography: 'Camera',
  catering: 'Utensils',
  music: 'Music',
  beauty: 'Paintbrush',
  clothing: 'Shirt',
  transport: 'Car',
  gifts: 'Gift',
};

export const categoriesData: Category[] = [
  {
    id: 'venues',
    name: 'Venues',
    nameAr: 'الأماكن',
    nameFr: 'Lieux',
    slug: 'venues',
    icon: ICONS.venues,
    subcategories: [
      {
        id: 'wedding-venues',
        name: 'Wedding Venues',
        nameAr: 'قاعات الزفاف',
        nameFr: 'Salles de Mariage',
        slug: 'wedding-venues',
      },
      {
        id: 'engagement-venues',
        name: 'Engagement Venues',
        nameAr: 'أماكن الخطوبة',
        nameFr: 'Lieux de Fiançailles',
        slug: 'engagement-venues',
      },
      {
        id: 'birthday-venues',
        name: 'Birthday Venues',
        nameAr: 'أماكن أعياد الميلاد',
        nameFr: 'Lieux d\'Anniversaire',
        slug: 'birthday-venues',
      },
      {
        id: 'baby-shower-venues',
        name: 'Baby Shower Venues',
        nameAr: 'أماكن حفلات استقبال المولود',
        nameFr: 'Lieux de Baby Shower',
        slug: 'baby-shower-venues',
      },
      {
        id: 'outdoor-spaces',
        name: 'Outdoor Spaces',
        nameAr: 'المساحات الخارجية',
        nameFr: 'Espaces Extérieurs',
        slug: 'outdoor-spaces',
      },
    ],
  },
  {
    id: 'decoration',
    name: 'Decoration',
    nameAr: 'الديكور',
    nameFr: 'Décoration',
    slug: 'decoration',
    icon: ICONS.decoration,
    subcategories: [
      {
        id: 'wedding-decoration',
        name: 'Wedding Decoration',
        nameAr: 'ديكور الزفاف',
        nameFr: 'Décoration de Mariage',
        slug: 'wedding-decoration',
      },
      {
        id: 'birthday-decoration',
        name: 'Birthday Decoration',
        nameAr: 'ديكور أعياد الميلاد',
        nameFr: 'Décoration d\'Anniversaire',
        slug: 'birthday-decoration',
      },
      {
        id: 'baby-shower-decoration',
        name: 'Baby Shower Decoration',
        nameAr: 'ديكور حفلات استقبال المولود',
        nameFr: 'Décoration de Baby Shower',
        slug: 'baby-shower-decoration',
      },
      {
        id: 'henna-party-decoration',
        name: 'Henna Party Decoration',
        nameAr: 'ديكور حفلات الحناء',
        nameFr: 'Décoration de Soirée Henné',
        slug: 'henna-party-decoration',
      },
      {
        id: 'balloon-decoration',
        name: 'Balloon Decoration',
        nameAr: 'ديكور البالونات',
        nameFr: 'Décoration de Ballons',
        slug: 'balloon-decoration',
      },
      {
        id: 'floral-decoration',
        name: 'Floral Decoration',
        nameAr: 'ديكور الزهور',
        nameFr: 'Décoration Florale',
        slug: 'floral-decoration',
      },
    ],
  },
  {
    id: 'photography',
    name: 'Photography & Videography',
    nameAr: 'التصوير الفوتوغرافي والفيديو',
    nameFr: 'Photo & Vidéo',
    slug: 'photography',
    icon: ICONS.photography,
    subcategories: [
      {
        id: 'wedding-photography',
        name: 'Wedding Photography',
        nameAr: 'تصوير الزفاف',
        nameFr: 'Photographie de Mariage',
        slug: 'wedding-photography',
      },
      {
        id: 'birthday-photography',
        name: 'Birthday Photography',
        nameAr: 'تصوير أعياد الميلاد',
        nameFr: 'Photographie d\'Anniversaire',
        slug: 'birthday-photography',
      },
      {
        id: 'baby-shower-photography',
        name: 'Baby Shower Photography',
        nameAr: 'تصوير حفلات استقبال المولود',
        nameFr: 'Photographie de Baby Shower',
        slug: 'baby-shower-photography',
      },
      {
        id: 'videographers',
        name: 'Videographers',
        nameAr: 'مصوري الفيديو',
        nameFr: 'Vidéographes',
        slug: 'videographers',
      },
      {
        id: '360-booths',
        name: '360 Booths',
        nameAr: 'أكشاك تصوير 360',
        nameFr: 'Cabines 360',
        slug: '360-booths',
      },
      {
        id: 'instant-print',
        name: 'Instant Print Services',
        nameAr: 'خدمات الطباعة الفورية',
        nameFr: 'Services d\'Impression Instantanée',
        slug: 'instant-print',
      },
    ],
  },
  {
    id: 'catering',
    name: 'Catering',
    nameAr: 'خدمات الطعام',
    nameFr: 'Traiteur',
    slug: 'catering',
    icon: ICONS.catering,
    subcategories: [
      {
        id: 'moroccan-catering',
        name: 'Moroccan Catering',
        nameAr: 'المأكولات المغربية',
        nameFr: 'Traiteur Marocain',
        slug: 'moroccan-catering',
      },
      {
        id: 'international-catering',
        name: 'International Catering',
        nameAr: 'المأكولات العالمية',
        nameFr: 'Traiteur International',
        slug: 'international-catering',
      },
      {
        id: 'cakes-pastries',
        name: 'Cakes & Pastries',
        nameAr: 'الكيك والمعجنات',
        nameFr: 'Gâteaux & Pâtisseries',
        slug: 'cakes-pastries',
        subSubCategories: [
          {
            id: 'birthday-cakes',
            name: 'Birthday Cakes',
            nameAr: 'كيك أعياد الميلاد',
            nameFr: 'Gâteaux d\'Anniversaire',
            slug: 'birthday-cakes',
          },
          {
            id: 'wedding-cakes',
            name: 'Wedding Cakes',
            nameAr: 'كيك الزفاف',
            nameFr: 'Gâteaux de Mariage',
            slug: 'wedding-cakes',
          },
        ],
      },
    ],
  },
  {
    id: 'music',
    name: 'Music & Animation',
    nameAr: 'الموسيقى والترفيه',
    nameFr: 'Musique & Animation',
    slug: 'music',
    icon: ICONS.music,
    subcategories: [
      {
        id: 'djs',
        name: 'DJs',
        nameAr: 'دي جي',
        nameFr: 'DJs',
        slug: 'djs',
      },
      {
        id: 'traditional-bands',
        name: 'Traditional Moroccan Bands',
        nameAr: 'الفرق المغربية التقليدية',
        nameFr: 'Groupes Traditionnels Marocains',
        slug: 'traditional-bands',
      },
      {
        id: 'singers-performers',
        name: 'Singers & Performers',
        nameAr: 'المغنين والمؤدين',
        nameFr: 'Chanteurs & Artistes',
        slug: 'singers-performers',
      },
      {
        id: 'animation-kids',
        name: 'Animation for Kids',
        nameAr: 'أنشطة للأطفال',
        nameFr: 'Animation pour Enfants',
        slug: 'animation-kids',
      },
      {
        id: 'entertainment-kids',
        name: 'Entertainment for Kids',
        nameAr: 'ترفيه للأطفال',
        nameFr: 'Divertissement pour Enfants',
        slug: 'entertainment-kids',
      },
    ],
  },
  {
    id: 'beauty',
    name: 'Beauty & Styling',
    nameAr: 'الجمال والتجميل',
    nameFr: 'Beauté',
    slug: 'beauty',
    icon: ICONS.beauty,
    subcategories: [
      {
        id: 'makeup-artists',
        name: 'Makeup Artists',
        nameAr: 'خبراء المكياج',
        nameFr: 'Maquilleurs',
        slug: 'makeup-artists',
        subSubCategories: [
          {
            id: 'makeup-women',
            name: 'For Women',
            nameAr: 'للنساء',
            nameFr: 'Pour Femmes',
            slug: 'makeup-women',
          },
          {
            id: 'makeup-men',
            name: 'For Men',
            nameAr: 'للرجال',
            nameFr: 'Pour Hommes',
            slug: 'makeup-men',
          },
        ],
      },
      {
        id: 'hair-stylists',
        name: 'Hair Stylists',
        nameAr: 'مصففي الشعر',
        nameFr: 'Coiffeurs',
        slug: 'hair-stylists',
        subSubCategories: [
          {
            id: 'hair-women',
            name: 'For Women',
            nameAr: 'للنساء',
            nameFr: 'Pour Femmes',
            slug: 'hair-women',
          },
          {
            id: 'hair-men',
            name: 'For Men',
            nameAr: 'للرجال',
            nameFr: 'Pour Hommes',
            slug: 'hair-men',
          },
        ],
      },
      {
        id: 'henna-artists',
        name: 'Henna Artists',
        nameAr: 'فناني الحناء',
        nameFr: 'Artistes de Henné',
        slug: 'henna-artists',
        subSubCategories: [
          {
            id: 'henna-women',
            name: 'For Women',
            nameAr: 'للنساء',
            nameFr: 'Pour Femmes',
            slug: 'henna-women',
          },
          {
            id: 'henna-men',
            name: 'For Men',
            nameAr: 'للرجال',
            nameFr: 'Pour Hommes',
            slug: 'henna-men',
          },
        ],
      },
      {
        id: 'salons-hammams',
        name: 'Salons & Hammams',
        nameAr: 'صالونات وحمامات',
        nameFr: 'Salons & Hammams',
        slug: 'salons-hammams',
        subSubCategories: [
          {
            id: 'salons-women',
            name: 'For Women',
            nameAr: 'للنساء',
            nameFr: 'Pour Femmes',
            slug: 'salons-women',
          },
          {
            id: 'salons-men',
            name: 'For Men',
            nameAr: 'للرجال',
            nameFr: 'Pour Hommes',
            slug: 'salons-men',
          },
        ],
      },
    ],
  },
  {
    id: 'clothing',
    name: 'Traditional Clothing',
    nameAr: 'الملابس التقليدية',
    nameFr: 'Habits Traditionnels',
    slug: 'clothing',
    icon: ICONS.clothing,
    subcategories: [
      {
        id: 'clothing-women',
        name: 'For Women',
        nameAr: 'للنساء',
        nameFr: 'Pour Femmes',
        slug: 'clothing-women',
        subSubCategories: [
          {
            id: 'caftan-takchita',
            name: 'Caftan & Takchita Rental',
            nameAr: 'تأجير القفطان والتكشيطة',
            nameFr: 'Location de Caftan & Takchita',
            slug: 'caftan-takchita',
          },
          {
            id: 'accessories-jewelry',
            name: 'Accessories & Jewelry',
            nameAr: 'الإكسسوارات والمجوهرات',
            nameFr: 'Accessoires & Bijoux',
            slug: 'accessories-jewelry',
          },
        ],
      },
      {
        id: 'clothing-men',
        name: 'For Men',
        nameAr: 'للرجال',
        nameFr: 'Pour Hommes',
        slug: 'clothing-men',
        subSubCategories: [
          {
            id: 'jellaba-jabador',
            name: 'Jellaba & Jabador Rental',
            nameAr: 'تأجير الجلابة والجبادور',
            nameFr: 'Location de Jellaba & Jabador',
            slug: 'jellaba-jabador',
          },
          {
            id: 'accessories-men',
            name: 'Accessories',
            nameAr: 'الإكسسوارات',
            nameFr: 'Accessoires',
            slug: 'accessories-men',
          },
        ],
      },
    ],
  },
  {
    id: 'transport',
    name: 'Transport',
    nameAr: 'النقل',
    nameFr: 'Transport',
    slug: 'transport',
    icon: ICONS.transport,
    subcategories: [
      {
        id: 'luxury-cars',
        name: 'Luxury Car Rentals',
        nameAr: 'تأجير السيارات الفخمة',
        nameFr: 'Location de Voitures de Luxe',
        slug: 'luxury-cars',
      },
      {
        id: 'guest-transport',
        name: 'Guest Transport',
        nameAr: 'نقل الضيوف',
        nameFr: 'Transport des Invités',
        slug: 'guest-transport',
      },
    ],
  },
  {
    id: 'gifts',
    name: 'Gifts & Invitations',
    nameAr: 'الهدايا والدعوات',
    nameFr: 'Cadeaux & Invitations',
    slug: 'gifts',
    icon: ICONS.gifts,
    subcategories: [
      {
        id: 'gift-boxes',
        name: 'Gift Boxes',
        nameAr: 'صناديق الهدايا',
        nameFr: 'Coffrets Cadeaux',
        slug: 'gift-boxes',
      },
      {
        id: 'invitation-cards',
        name: 'Invitation Cards',
        nameAr: 'بطاقات الدعوة',
        nameFr: 'Cartes d\'Invitation',
        slug: 'invitation-cards',
      },
    ],
  },
];

// Function to get all categories
export const getCategories = () => categoriesData;

// Function to find a specific category by slug
export const getCategoryBySlug = (slug: string) => {
  return categoriesData.find((category) => category.slug === slug);
};

// Function to find a specific subcategory by slug
export const getSubcategoryBySlug = (categorySlug: string, subcategorySlug: string) => {
  const category = getCategoryBySlug(categorySlug);
  return category?.subcategories.find((subcat) => subcat.slug === subcategorySlug);
};

// Function to find a specific sub-subcategory by slug
export const getSubSubcategoryBySlug = (categorySlug: string, subcategorySlug: string, subSubcategorySlug: string) => {
  const subcategory = getSubcategoryBySlug(categorySlug, subcategorySlug);
  return subcategory?.subSubCategories?.find((subSubcat) => subSubcat.slug === subSubcategorySlug);
};