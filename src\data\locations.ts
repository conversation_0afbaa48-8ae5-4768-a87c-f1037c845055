import { Location } from '../types';

export const locationsData: Location[] = [
  {
    id: 'casablanca',
    name: 'Casablanca',
    nameAr: 'الدار البيضاء',
    nameFr: 'Casablanca',
  },
  {
    id: 'rabat',
    name: '<PERSON><PERSON>',
    nameAr: 'الرباط',
    nameFr: 'Ra<PERSON>',
  },
  {
    id: 'marrakech',
    name: 'Marrakech',
    nameAr: 'مراكش',
    nameFr: 'Marrakech',
  },
  {
    id: 'fes',
    name: '<PERSON><PERSON>',
    nameAr: 'فاس',
    nameFr: '<PERSON><PERSON>',
  },
  {
    id: 'tangier',
    name: '<PERSON><PERSON>',
    nameAr: 'طنجة',
    nameFr: 'Tanger',
  },
  {
    id: 'agadir',
    name: 'Agadir',
    nameAr: 'أكادير',
    nameFr: 'Agadir',
  },
  {
    id: 'essaouira',
    name: 'Essaouira',
    nameAr: 'الصويرة',
    nameFr: 'Essaouira',
  },
  {
    id: 'meknes',
    name: '<PERSON>k<PERSON>',
    nameAr: 'مكناس',
    nameFr: 'Mekn<PERSON>',
  },
  {
    id: 'tetouan',
    name: 'Teto<PERSON>',
    nameAr: 'تطوان',
    nameFr: 'Tétouan',
  },
  {
    id: 'chefchaouen',
    name: 'Chefchaouen',
    nameAr: 'شفشاون',
    nameFr: 'Chefchaouen',
  },
];

export const getLocations = () => locationsData;

export const getLocationById = (id: string) => {
  return locationsData.find((location) => location.id === id);
};