import { Toaster } from '@/components/ui/sonner';
import { TooltipProvider } from '@/components/ui/tooltip';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import Layout from './components/layout/Layout';
import Footer from './components/layout/Footer';
import Index from './pages/Index';
import CategoryPage from './pages/categories/CategoryPage';
import ServiceDetails from './pages/ServiceDetails';
import NotFound from './pages/NotFound';

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <BrowserRouter>
        <Routes>
          <Route element={<Layout />}>
            <Route index element={<Index />} />
            <Route path="categories/:categorySlug" element={<CategoryPage />} />
            <Route path="categories/:categorySlug/:subcategorySlug" element={<CategoryPage />} />
            <Route path="categories/:categorySlug/:subcategorySlug/:subSubCategorySlug" element={<CategoryPage />} />
            <Route path="service/:serviceId" element={<ServiceDetails />} />
            <Route path="*" element={<NotFound />} />
          </Route>
        </Routes>
        <Footer />
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
