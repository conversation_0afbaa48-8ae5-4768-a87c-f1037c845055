import { Link } from 'react-router-dom';
import { Building, Sparkles, Camera, Utensils, Music, Paintbrush, Shirt, Car, Gift } from 'lucide-react';
import { categoriesData } from '@/data/categories';
import { Card, CardContent } from '@/components/ui/card';

// Icon mapping
const iconMap = {
  'Building': Building,
  'Sparkles': Sparkles,
  'Camera': Camera,
  'Utensils': Utensils,
  'Music': Music,
  'Paintbrush': Paintbrush,
  'Shirt': Shirt,
  'Car': Car,
  'Gift': Gift,
};

export default function CategoryShowcase() {
  // Get the icon component by name
  const getIconComponent = (iconName: string) => {
    return iconMap[iconName as keyof typeof iconMap] || Building;
  };

  return (
    <section className="py-12 md:py-16 bg-muted/30">
      <div className="container mx-auto px-4">
        <h2 className="font-serif text-2xl md:text-3xl font-semibold text-center mb-10">
          Explore by Category
        </h2>
        
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
          {categoriesData.map((category) => {
            const IconComponent = getIconComponent(category.icon);
            
            return (
              <Link key={category.id} to={`/categories/${category.slug}`}>
                <Card className="h-full overflow-hidden hover:border-morocco-teal/50 transition-all duration-200 hover-lift">
                  <CardContent className="flex flex-col items-center justify-center p-6 text-center">
                    <div className="p-3 rounded-full bg-morocco-teal/10 mb-4">
                      <IconComponent className="h-6 w-6 text-morocco-teal" />
                    </div>
                    <h3 className="font-medium">{category.name}</h3>
                    <p className="text-sm text-muted-foreground mt-1">
                      {category.subcategories.length} Services
                    </p>
                  </CardContent>
                </Card>
              </Link>
            );
          })}
        </div>
      </div>
    </section>
  );
}