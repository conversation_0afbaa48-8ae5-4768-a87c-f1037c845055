import { Link } from 'react-router-dom';
import { Instagram, Facebook, Twitter } from 'lucide-react';
import { categoriesData } from '@/data/categories';

export default function Footer() {
  const currentYear = new Date().getFullYear();
  
  return (
    <footer className="bg-morocco-navy/5 border-t border-border">
      <div className="container mx-auto px-4 py-10 md:py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Brand & Description */}
          <div>
            <h3 className="font-serif text-xl font-semibold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-morocco-teal to-morocco-gold">
              Moroccan Celebrations
            </h3>
            <p className="text-muted-foreground mb-4">
              The premier platform for discovering top-quality services for all your Moroccan celebration needs.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-morocco-teal hover:text-morocco-gold transition-colors">
                <Instagram size={18} />
                <span className="sr-only">Instagram</span>
              </a>
              <a href="#" className="text-morocco-teal hover:text-morocco-gold transition-colors">
                <Facebook size={18} />
                <span className="sr-only">Facebook</span>
              </a>
              <a href="#" className="text-morocco-teal hover:text-morocco-gold transition-colors">
                <Twitter size={18} />
                <span className="sr-only">Twitter</span>
              </a>
            </div>
          </div>
          
          {/* Categories */}
          <div>
            <h4 className="font-medium mb-4">Categories</h4>
            <ul className="space-y-2">
              {categoriesData.slice(0, 6).map((category) => (
                <li key={category.id}>
                  <Link 
                    to={`/categories/${category.slug}`} 
                    className="text-muted-foreground hover:text-morocco-teal transition-colors"
                  >
                    {category.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
          
          {/* Quick Links */}
          <div>
            <h4 className="font-medium mb-4">Quick Links</h4>
            <ul className="space-y-2">
              <li>
                <Link to="/" className="text-muted-foreground hover:text-morocco-teal transition-colors">
                  Home
                </Link>
              </li>
              <li>
                <Link to="/about" className="text-muted-foreground hover:text-morocco-teal transition-colors">
                  About Us
                </Link>
              </li>
              <li>
                <Link to="/featured" className="text-muted-foreground hover:text-morocco-teal transition-colors">
                  Featured Services
                </Link>
              </li>
              <li>
                <Link to="/how-it-works" className="text-muted-foreground hover:text-morocco-teal transition-colors">
                  How It Works
                </Link>
              </li>
              <li>
                <Link to="/contact" className="text-muted-foreground hover:text-morocco-teal transition-colors">
                  Contact
                </Link>
              </li>
            </ul>
          </div>
          
          {/* Contact */}
          <div>
            <h4 className="font-medium mb-4">Contact</h4>
            <ul className="space-y-2 text-muted-foreground">
              <li>Email: <EMAIL></li>
              <li>Phone: +212 5XX-XXXXXX</li>
              <li>Address: 123 Boulevard Mohammed V, Casablanca, Morocco</li>
            </ul>
          </div>
        </div>
        
        <div className="border-t border-border/50 mt-10 pt-6 flex flex-col md:flex-row justify-between items-center">
          <p className="text-sm text-muted-foreground mb-4 md:mb-0">
            © {currentYear} Moroccan Celebration Services. All rights reserved.
          </p>
          <div className="flex space-x-6">
            <Link to="/privacy" className="text-sm text-muted-foreground hover:text-morocco-teal transition-colors">
              Privacy Policy
            </Link>
            <Link to="/terms" className="text-sm text-muted-foreground hover:text-morocco-teal transition-colors">
              Terms of Service
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
}