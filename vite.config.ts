import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react-swc';
import path from 'path';

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  server: {
    watch: {
      // Reduce the number of files being watched
      usePolling: true,
      interval: 1000,
    },
  },
  // Exclude node_modules from being watched
  optimizeDeps: {
    exclude: ['node_modules'],
  },
  build: {
    // Reduce the build size
    sourcemap: false,
    // Improve performance
    chunkSizeWarningLimit: 1000,
  },
}));
