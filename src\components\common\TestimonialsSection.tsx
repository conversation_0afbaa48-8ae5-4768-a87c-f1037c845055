import { Star } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';

// Sample testimonial data
const testimonials = [
  {
    id: 1,
    name: '<PERSON><PERSON>',
    text: 'We found our dream wedding venue through this platform. The filters made it easy to narrow down options based on our specific needs and budget. Highly recommended!',
    rating: 5,
    event: 'Wedding',
    location: 'Marrakech',
  },
  {
    id: 2,
    name: '<PERSON><PERSON>',
    text: 'The variety of traditional music bands available here is impressive. We were able to book an amazing Andalusian group for our engagement party within days.',
    rating: 5,
    event: 'Engagement',
    location: 'Fes',
  },
  {
    id: 3,
    name: '<PERSON><PERSON>',
    text: 'Finding a talented henna artist was so easy with this platform. The photos in the portfolio helped me choose the perfect style for my henna party.',
    rating: 4,
    event: 'Henna Party',
    location: 'Casablanca',
  },
];

export default function TestimonialsSection() {
  return (
    <section className="py-12 md:py-16">
      <div className="container mx-auto px-4">
        <h2 className="font-serif text-2xl md:text-3xl font-semibold text-center mb-3">
          What Our Clients Say
        </h2>
        <p className="text-center text-muted-foreground mb-10 max-w-2xl mx-auto">
          Hear from couples and clients who found the perfect services for their celebrations
        </p>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {testimonials.map((testimonial) => (
            <Card key={testimonial.id} className="overflow-hidden border-morocco-gold/20 hover:border-morocco-gold/40 transition-all duration-200">
              <CardContent className="p-6">
                {/* Rating */}
                <div className="flex mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`h-4 w-4 ${
                        i < testimonial.rating
                          ? 'text-morocco-gold fill-morocco-gold'
                          : 'text-muted'
                      }`}
                    />
                  ))}
                </div>
                
                {/* Testimonial text */}
                <p className="mb-6 text-sm md:text-base">"{testimonial.text}"</p>
                
                {/* Client info */}
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">{testimonial.name}</h4>
                    <p className="text-sm text-muted-foreground">
                      {testimonial.event} in {testimonial.location}
                    </p>
                  </div>
                  
                  {/* Optional: client avatar could go here */}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}