import { useState, useEffect } from 'react';
import ServiceCard from './ServiceCard';
import { Button } from '@/components/ui/button';
import { ServiceProvider } from '@/types';
import { getFeaturedProviders } from '@/data/providers';

export default function FeaturedServices() {
  const [featuredProviders, setFeaturedProviders] = useState<ServiceProvider[]>([]);
  
  useEffect(() => {
    // In a real app, this would be an API call
    setFeaturedProviders(getFeaturedProviders());
  }, []);
  
  return (
    <section className="py-12 md:py-16">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center mb-8">
          <h2 className="font-serif text-2xl md:text-3xl font-semibold">
            Featured Services
          </h2>
          <Button variant="outline" className="border-morocco-teal text-morocco-teal hover:bg-morocco-teal hover:text-white">
            View All
          </Button>
        </div>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {featuredProviders.map((provider) => (
            <ServiceCard key={provider.id} provider={provider} />
          ))}
        </div>
      </div>
    </section>
  );
}