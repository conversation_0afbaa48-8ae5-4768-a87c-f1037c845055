import { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { ChevronRight } from 'lucide-react';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import ServiceCard from '@/components/common/ServiceCard';
import { getCategoryBySlug } from '@/data/categories';
import { getProvidersByCategory } from '@/data/providers';
import { ServiceProvider, Category } from '@/types';

export default function CategoryPage() {
  const { categorySlug } = useParams<{ categorySlug: string }>();
  const [category, setCategory] = useState<Category | null>(null);
  const [providers, setProviders] = useState<ServiceProvider[]>([]);
  const [sortBy, setSortBy] = useState<string>('featured');
  
  useEffect(() => {
    if (categorySlug) {
      const categoryData = getCategoryBySlug(categorySlug);
      if (categoryData) {
        setCategory(categoryData);
        // Fetch providers by category
        setProviders(getProvidersByCategory(categoryData.id));
      }
    }
  }, [categorySlug]);
  
  const handleSortChange = (value: string) => {
    setSortBy(value);
    
    // Sort providers based on selected option
    const sortedProviders = [...providers];
    
    switch (value) {
      case 'featured':
        sortedProviders.sort((a, b) => (b.featured ? 1 : 0) - (a.featured ? 1 : 0));
        break;
      case 'rating':
        sortedProviders.sort((a, b) => (b.rating || 0) - (a.rating || 0));
        break;
      case 'priceAsc':
        sortedProviders.sort((a, b) => {
          const priceA = a.priceRange?.length || 0;
          const priceB = b.priceRange?.length || 0;
          return priceA - priceB;
        });
        break;
      case 'priceDesc':
        sortedProviders.sort((a, b) => {
          const priceA = a.priceRange?.length || 0;
          const priceB = b.priceRange?.length || 0;
          return priceB - priceA;
        });
        break;
      default:
        break;
    }
    
    setProviders(sortedProviders);
  };
  
  if (!category) {
    return (
      <div className="container mx-auto px-4 py-12">
        <p>Category not found.</p>
      </div>
    );
  }
  
  return (
    <div>
      {/* Category header */}
      <div className="bg-muted/30 py-8 md:py-12 mb-6">
        <div className="container mx-auto px-4">
          <div className="flex items-center text-sm text-muted-foreground mb-4">
            <span>Home</span>
            <ChevronRight className="h-4 w-4 mx-2" />
            <span>{category.name}</span>
          </div>
          
          <h1 className="font-serif text-3xl md:text-4xl font-bold mb-4">{category.name}</h1>
          
          <p className="text-muted-foreground max-w-3xl">
            Discover the best {category.name.toLowerCase()} for your Moroccan celebration. 
            Browse our curated selection of top-rated service providers.
          </p>
        </div>
      </div>
      
      <div className="container mx-auto px-4 py-6">
        {/* Filters and sorting */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8">
          <div>
            <h2 className="font-semibold">{providers.length} Services Available</h2>
          </div>
          
          <div className="flex items-center mt-4 sm:mt-0">
            <span className="text-sm text-muted-foreground mr-2">Sort by:</span>
            <Select value={sortBy} onValueChange={handleSortChange}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Featured" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="featured">Featured</SelectItem>
                <SelectItem value="rating">Highest Rating</SelectItem>
                <SelectItem value="priceAsc">Price: Low to High</SelectItem>
                <SelectItem value="priceDesc">Price: High to Low</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        
        <Separator className="mb-8" />
        
        {/* Subcategories section */}
        <div className="mb-10">
          <h3 className="font-serif text-xl font-medium mb-4">Subcategories</h3>
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
            {category.subcategories.map((subcategory) => (
              <a 
                href={`/categories/${category.slug}/${subcategory.slug}`}
                key={subcategory.id}
                className="p-4 border border-border rounded-md hover:border-morocco-teal/50 hover:bg-morocco-teal/5 transition-colors"
              >
                <h4 className="font-medium">{subcategory.name}</h4>
              </a>
            ))}
          </div>
        </div>
        
        {/* Services grid */}
        {providers.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {providers.map((provider) => (
              <ServiceCard key={provider.id} provider={provider} />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <p className="text-muted-foreground">No services found for this category.</p>
          </div>
        )}
      </div>
    </div>
  );
}