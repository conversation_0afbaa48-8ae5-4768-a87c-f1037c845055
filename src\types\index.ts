// Service provider types
export interface ServiceProvider {
  id: string;
  name: string;
  description: string;
  category: string;
  subcategory: string;
  location: string;
  contact: {
    phone?: string;
    email?: string;
    website?: string;
    social?: {
      instagram?: string;
      facebook?: string;
    };
  };
  rating?: number;
  images: string[];
  featured?: boolean;
  priceRange?: string;
}

// Category structure types
export interface SubSubCategory {
  id: string;
  name: string;
  nameAr?: string;
  nameFr?: string;
  slug: string;
}

export interface SubCategory {
  id: string;
  name: string;
  nameAr?: string;
  nameFr?: string;
  slug: string;
  subSubCategories?: SubSubCategory[];
}

export interface Category {
  id: string;
  name: string;
  nameAr?: string;
  nameFr?: string;
  slug: string;
  icon: string;
  subcategories: SubCategory[];
}

// Location types
export interface Location {
  id: string;
  name: string;
  nameAr?: string;
  nameFr?: string;
}

// Search and filter types
export interface SearchFilters {
  category?: string;
  subcategory?: string;
  subSubcategory?: string;
  location?: string;
  priceRange?: [number, number];
  rating?: number;
}